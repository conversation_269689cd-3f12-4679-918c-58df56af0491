#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel批量导入工具
用于读取Excel文件并批量调用/addbao接口创建项目
"""

import pandas as pd
import requests
import json
import time
from datetime import datetime
import sys
import os
from typing import Dict, List, Tuple, Optional

class ExcelBatchImporter:
    def __init__(self, server_url: str = "http://localhost:5000"):
        """
        初始化批量导入工具
        
        Args:
            server_url: 服务器地址，默认为本地服务器
        """
        self.server_url = server_url.rstrip('/')
        self.session = requests.Session()
        self.access_token = None
        
    def login(self, username: str, password: str, captcha: str = None, captcha_id: str = None) -> bool:
        """
        用户登录获取访问令牌
        
        Args:
            username: 用户名（手机号）
            password: 密码
            captcha: 验证码（如果需要）
            captcha_id: 验证码ID（如果需要）
            
        Returns:
            bool: 登录是否成功
        """
        try:
            login_data = {
                "username": username,
                "password": password
            }
            
            headers = {}
            if captcha and captcha_id:
                login_data["captcha"] = captcha
                headers["X-Captcha-ID"] = captcha_id
            
            response = self.session.post(
                f"{self.server_url}/login",
                json=login_data,
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                self.access_token = result.get("access_token")
                print(f"✓ 登录成功，用户: {result.get('username')}, 城市: {result.get('city')}")
                return True
            else:
                error_msg = response.json().get("msg", "登录失败")
                print(f"✗ 登录失败: {error_msg}")
                return False
                
        except Exception as e:
            print(f"✗ 登录异常: {str(e)}")
            return False
    
    def get_captcha(self) -> Tuple[Optional[bytes], Optional[str]]:
        """
        获取验证码
        
        Returns:
            Tuple[bytes, str]: 验证码图片数据和验证码ID
        """
        try:
            response = self.session.get(f"{self.server_url}/captcha")
            if response.status_code == 200:
                captcha_id = response.headers.get("X-Captcha-ID")
                return response.content, captcha_id
            return None, None
        except Exception as e:
            print(f"✗ 获取验证码失败: {str(e)}")
            return None, None
    
    def read_excel_file(self, file_path: str) -> pd.DataFrame:
        """
        读取Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            pd.DataFrame: 读取的数据
        """
        try:
            # 支持.xlsx和.xls格式
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path, engine='openpyxl')
            elif file_path.endswith('.xls'):
                df = pd.read_excel(file_path, engine='xlrd')
            else:
                raise ValueError("不支持的文件格式，请使用.xlsx或.xls文件")
            
            print(f"✓ 成功读取Excel文件: {file_path}")
            print(f"  - 总行数: {len(df)}")
            print(f"  - 列名: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            print(f"✗ 读取Excel文件失败: {str(e)}")
            raise
    
    def validate_excel_columns(self, df: pd.DataFrame) -> bool:
        """
        验证Excel文件列名是否正确
        
        Args:
            df: DataFrame数据
            
        Returns:
            bool: 验证是否通过
        """
        required_columns = [
            'bao_city',    # 城市
            'gds',         # 供电所
            'gdlx',        # 供电类型
            'bao_bm',      # 报表编码
            'bao_name',    # 报表名称
            'usercode',    # 用户代码
            'htrl',        # 合同容量
            'bao_lx',      # 报表类型
            'sj_ywsl',     # 实际业务数量
            'sj_gdsj'      # 实际供电时间
        ]
        
        missing_columns = []
        for col in required_columns:
            if col not in df.columns:
                missing_columns.append(col)
        
        if missing_columns:
            print(f"✗ Excel文件缺少必需的列: {missing_columns}")
            print(f"  需要的列: {required_columns}")
            print(f"  当前的列: {list(df.columns)}")
            return False
        
        print("✓ Excel文件列验证通过")
        return True
    
    def format_datetime_field(self, value) -> Optional[str]:
        """
        格式化日期时间字段
        
        Args:
            value: 原始值
            
        Returns:
            str: 格式化后的日期时间字符串，格式为 YYYY-MM-DD HH:MM:SS
        """
        if pd.isna(value) or value is None or str(value).strip() == '':
            return None
            
        try:
            # 如果已经是datetime对象
            if isinstance(value, datetime):
                return value.strftime("%Y-%m-%d %H:%M:%S")
            
            # 如果是pandas的Timestamp
            if hasattr(value, 'to_pydatetime'):
                return value.to_pydatetime().strftime("%Y-%m-%d %H:%M:%S")
            
            # 尝试解析字符串
            value_str = str(value).strip()
            
            # 常见的日期格式
            date_formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y/%m/%d %H:%M:%S", 
                "%Y-%m-%d",
                "%Y/%m/%d",
                "%m/%d/%Y",
                "%d/%m/%Y"
            ]
            
            for fmt in date_formats:
                try:
                    dt = datetime.strptime(value_str, fmt)
                    # 如果只有日期没有时间，默认设置为00:00:00
                    if fmt in ["%Y-%m-%d", "%Y/%m/%d", "%m/%d/%Y", "%d/%m/%Y"]:
                        return dt.strftime("%Y-%m-%d 00:00:00")
                    else:
                        return dt.strftime("%Y-%m-%d %H:%M:%S")
                except ValueError:
                    continue
            
            print(f"⚠ 无法解析日期格式: {value_str}")
            return None
            
        except Exception as e:
            print(f"⚠ 日期格式化异常: {str(e)}, 值: {value}")
            return None

    def prepare_row_data(self, row: pd.Series) -> Dict:
        """
        准备单行数据用于API调用

        Args:
            row: DataFrame的一行数据

        Returns:
            Dict: 准备好的数据字典
        """
        try:
            # 基础字段映射
            data = {
                "bao_city": str(row['bao_city']).strip() if pd.notna(row['bao_city']) else "",
                "gds": str(row['gds']).strip() if pd.notna(row['gds']) else "",
                "gdlx": str(row['gdlx']).strip() if pd.notna(row['gdlx']) else None,
                "bao_bm": str(row['bao_bm']).strip() if pd.notna(row['bao_bm']) else "",
                "bao_name": str(row['bao_name']).strip() if pd.notna(row['bao_name']) else "",
                "usercode": str(row['usercode']).strip() if pd.notna(row['usercode']) else "",
                "bao_lx": str(row['bao_lx']).strip() if pd.notna(row['bao_lx']) else "",
            }

            # 处理合同容量
            if pd.notna(row['htrl']) and str(row['htrl']).strip():
                try:
                    data["htrl"] = float(row['htrl'])
                except (ValueError, TypeError):
                    data["htrl"] = None
            else:
                data["htrl"] = None

            # 处理时间字段
            data["sj_ywsl"] = self.format_datetime_field(row['sj_ywsl'])
            data["sj_gdsj"] = self.format_datetime_field(row['sj_gdsj'])

            return data

        except Exception as e:
            print(f"✗ 准备行数据失败: {str(e)}")
            raise

    def call_addbao_api(self, data: Dict) -> Tuple[bool, str, Optional[int]]:
        """
        调用/addbao接口创建项目

        Args:
            data: 项目数据

        Returns:
            Tuple[bool, str, int]: (成功标志, 消息, 项目ID)
        """
        try:
            if not self.access_token:
                return False, "未登录，请先调用login方法", None

            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }

            response = self.session.post(
                f"{self.server_url}/addbao",
                json=data,
                headers=headers
            )

            result = response.json()

            if response.status_code == 200:
                bao_id = result.get("bao_id")
                return True, result.get("msg", "创建成功"), bao_id
            else:
                return False, result.get("msg", "创建失败"), None

        except Exception as e:
            return False, f"API调用异常: {str(e)}", None

    def validate_row_data(self, data: Dict, row_index: int) -> Tuple[bool, List[str]]:
        """
        验证单行数据

        Args:
            data: 数据字典
            row_index: 行索引

        Returns:
            Tuple[bool, List[str]]: (验证是否通过, 错误信息列表)
        """
        errors = []

        # 必填字段验证
        required_fields = {
            "bao_city": "城市",
            "gds": "供电所",
            "bao_bm": "报表编码",
            "bao_name": "报表名称",
            "usercode": "用户代码",
            "bao_lx": "报表类型"
        }

        for field, field_name in required_fields.items():
            if not data.get(field) or str(data[field]).strip() == "":
                errors.append(f"{field_name}不能为空")

        # 报表编码格式验证
        if data.get("bao_bm") and not str(data["bao_bm"]).isdigit():
            errors.append("报表编码必须是数字")

        # 合同容量验证
        if data.get("htrl") is not None and data["htrl"] < 0:
            errors.append("合同容量不能为负数")

        # 工单类型验证
        valid_gdlx = ["增容", "临时用电", "新装"]
        if data.get("gdlx") and data["gdlx"] not in valid_gdlx:
            errors.append(f"工单类型错误，必须是：{', '.join(valid_gdlx)}")

        return len(errors) == 0, errors

    def batch_import(self, file_path: str, max_retries: int = 3, delay_between_requests: float = 0.1) -> Dict:
        """
        批量导入Excel数据

        Args:
            file_path: Excel文件路径
            max_retries: 最大重试次数
            delay_between_requests: 请求间隔时间（秒）

        Returns:
            Dict: 导入结果统计
        """
        print(f"开始批量导入Excel文件: {file_path}")
        print("=" * 60)

        try:
            # 读取Excel文件
            df = self.read_excel_file(file_path)

            # 验证列名
            if not self.validate_excel_columns(df):
                return {"success": False, "message": "Excel文件列验证失败"}

            # 初始化结果统计
            results = {
                "total": len(df),
                "success": 0,
                "failed": 0,
                "skipped": 0,
                "details": [],
                "success_records": [],
                "failed_records": [],
                "skipped_records": []
            }

            print(f"\n开始处理 {len(df)} 条记录...")
            print("-" * 60)

            for index, row in df.iterrows():
                row_num = index + 1
                print(f"处理第 {row_num}/{len(df)} 条记录...")

                try:
                    # 准备数据
                    data = self.prepare_row_data(row)

                    # 验证数据
                    is_valid, validation_errors = self.validate_row_data(data, row_num)

                    if not is_valid:
                        error_msg = f"数据验证失败: {'; '.join(validation_errors)}"
                        print(f"  ✗ 第{row_num}行跳过: {error_msg}")
                        results["skipped"] += 1
                        results["skipped_records"].append({
                            "row": row_num,
                            "data": data,
                            "error": error_msg
                        })
                        continue

                    # 调用API创建项目
                    success = False
                    last_error = ""

                    for retry in range(max_retries):
                        api_success, api_message, bao_id = self.call_addbao_api(data)

                        if api_success:
                            success = True
                            print(f"  ✓ 第{row_num}行成功: {data['bao_name']} (ID: {bao_id})")
                            results["success"] += 1
                            results["success_records"].append({
                                "row": row_num,
                                "data": data,
                                "bao_id": bao_id,
                                "message": api_message
                            })
                            break
                        else:
                            last_error = api_message
                            if retry < max_retries - 1:
                                print(f"  ⚠ 第{row_num}行重试 {retry + 1}/{max_retries}: {api_message}")
                                time.sleep(delay_between_requests * (retry + 1))  # 递增延迟
                            else:
                                print(f"  ✗ 第{row_num}行失败: {api_message}")

                    if not success:
                        results["failed"] += 1
                        results["failed_records"].append({
                            "row": row_num,
                            "data": data,
                            "error": last_error
                        })

                    # 请求间隔
                    if delay_between_requests > 0:
                        time.sleep(delay_between_requests)

                except Exception as e:
                    error_msg = f"处理异常: {str(e)}"
                    print(f"  ✗ 第{row_num}行异常: {error_msg}")
                    results["failed"] += 1
                    results["failed_records"].append({
                        "row": row_num,
                        "data": {},
                        "error": error_msg
                    })

            # 输出最终结果
            print("\n" + "=" * 60)
            print("批量导入完成!")
            print(f"总计: {results['total']} 条")
            print(f"成功: {results['success']} 条")
            print(f"失败: {results['failed']} 条")
            print(f"跳过: {results['skipped']} 条")
            print("=" * 60)

            return results

        except Exception as e:
            error_msg = f"批量导入异常: {str(e)}"
            print(f"✗ {error_msg}")
            return {"success": False, "message": error_msg}

    def save_import_report(self, results: Dict, output_file: str = None) -> str:
        """
        保存导入报告到文件

        Args:
            results: 导入结果
            output_file: 输出文件路径，如果为None则自动生成

        Returns:
            str: 报告文件路径
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"import_report_{timestamp}.txt"

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("Excel批量导入报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"导入时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总计: {results['total']} 条\n")
                f.write(f"成功: {results['success']} 条\n")
                f.write(f"失败: {results['failed']} 条\n")
                f.write(f"跳过: {results['skipped']} 条\n\n")

                if results['success_records']:
                    f.write("成功记录:\n")
                    f.write("-" * 40 + "\n")
                    for record in results['success_records']:
                        f.write(f"第{record['row']}行: {record['data']['bao_name']} (ID: {record['bao_id']})\n")
                    f.write("\n")

                if results['failed_records']:
                    f.write("失败记录:\n")
                    f.write("-" * 40 + "\n")
                    for record in results['failed_records']:
                        f.write(f"第{record['row']}行: {record.get('data', {}).get('bao_name', '未知')} - {record['error']}\n")
                    f.write("\n")

                if results['skipped_records']:
                    f.write("跳过记录:\n")
                    f.write("-" * 40 + "\n")
                    for record in results['skipped_records']:
                        f.write(f"第{record['row']}行: {record.get('data', {}).get('bao_name', '未知')} - {record['error']}\n")

            print(f"✓ 导入报告已保存到: {output_file}")
            return output_file

        except Exception as e:
            print(f"✗ 保存报告失败: {str(e)}")
            return ""


def main():
    """
    主函数 - 命令行使用示例
    """
    print("Excel批量导入工具")
    print("=" * 60)

    # 配置参数
    SERVER_URL = "http://localhost:5000"  # 修改为您的服务器地址
    EXCEL_FILE = "C:\\Users\\<USER>\\Desktop\\高压工单修改后.xlsx"  # 修改为您的Excel文件路径

    # 用户登录信息
    USERNAME = input("请输入用户名(手机号): ").strip()
    PASSWORD = input("请输入密码: ").strip()

    if not USERNAME or not PASSWORD:
        print("✗ 用户名和密码不能为空")
        return

    # 创建导入工具实例
    importer = ExcelBatchImporter(SERVER_URL)

    # 获取验证码（如果需要）
    print("\n正在获取验证码...")
    captcha_data, captcha_id = importer.get_captcha()

    captcha_code = None
    if captcha_data and captcha_id:
        # 保存验证码图片
        with open("captcha.jpg", "wb") as f:
            f.write(captcha_data)
        print("✓ 验证码已保存为 captcha.jpg，请查看并输入验证码")
        captcha_code = input("请输入验证码: ").strip()

    # 登录
    print("\n正在登录...")
    if not importer.login(USERNAME, PASSWORD, captcha_code, captcha_id):
        print("✗ 登录失败，程序退出")
        return

    # 检查Excel文件
    if not os.path.exists(EXCEL_FILE):
        print(f"✗ Excel文件不存在: {EXCEL_FILE}")
        print("请确保Excel文件存在，并包含以下列:")
        print("  - bao_city (城市)")
        print("  - gds (供电所)")
        print("  - gdlx (供电类型)")
        print("  - bao_bm (报表编码)")
        print("  - bao_name (报表名称)")
        print("  - usercode (用户代码)")
        print("  - htrl (合同容量)")
        print("  - bao_lx (报表类型)")
        print("  - sj_ywsl (实际业务数量)")
        print("  - sj_gdsj (实际供电时间)")
        return

    # 执行批量导入
    print(f"\n开始导入Excel文件: {EXCEL_FILE}")
    results = importer.batch_import(
        file_path=EXCEL_FILE,
        max_retries=3,
        delay_between_requests=0.1
    )

    # 保存导入报告
    if results.get("total", 0) > 0:
        importer.save_import_report(results)

    print("\n程序执行完成!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行异常: {str(e)}")
        import traceback
        traceback.print_exc()
